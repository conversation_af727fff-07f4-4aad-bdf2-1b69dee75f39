import { NewsWidgetPageContentWrapperPML } from '../NewsWidgetPage';
import './index.scss';
import useNewsWidgetBarColors from '../../hooks/useNewsWidgetBarColors';

const NewsWidgetListWrapper = () => (
  // pass data=[] to PML List Page to render
  <NewsWidgetPageContentWrapperPML data={[]} isListPage />
);

const NewsWidgetListPageRoute = () => {
  useNewsWidgetBarColors();

  return <NewsWidgetListWrapper />;
};

export default NewsWidgetListPageRoute;
