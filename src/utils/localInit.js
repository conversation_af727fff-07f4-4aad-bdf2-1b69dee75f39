// import { getCookieValue } from './commonUtil';
// import { COOKIES } from './constants';

// const ssoToken = getCookieValue(COOKIES.SSO_TOKEN);
// const userAgent = JSON.parse(getCookieValue(COOKIES.USER_AGENT));
// const authorization = getCookieValue(COOKIES.AUTHORIZATION);
// const uid = getCookieValue(COOKIES.UID);

const bridgeData = {
  origin: 'paytm',
  device_type: 'android',
  client_type: 'android',
  isLogin: true,
  isIos: false,
  Authorization:
    'Basic cGF5dG0tbW9uZXk6c05VcnBaMnc3MjJJdXZVMnBnNnhPTThhbGRnWGtSOW0=',
  sso_token: '87d98b3f-aef2-4dc7-8a8b-a0158ec03500', // replace with sso token
  userId: '231478903', // replace with user id
  appVersionName: '9.30.1120',
  h5Version: '1.0.11-9.15.0-MB-H5-4468',
  loggedInType: 'deviceBinded',
  deviceName: 'motorola Moto G (5S)',
  H5NativeDeeplinkData: '',
  twoFAToken: 'txk+VW2Sf/cyqh6Upl/AZOK89RqBMxvoaYy71ko6IXE=',
  deviceId: '8e648653-b432-5e0c-a532-1be0ef495e50',
};

export default bridgeData;
