/* eslint-disable import/no-cycle */
import { LOGIN_URL } from '../config/urlConfig';
import DeviceInfoProvider from '../provider/DeviceInfoProvider';

import {
  generateQueryParamsString,
  getCookieValue,
  getInitialQueryParams,
  getMobileOperatingSystem,
  getQueryParams,
  getQueryParamsFromString,
  isDarkMode,
  isIosBuild,
  isPaytm,
  log,
} from './commonUtil';
import { COOKIES } from './constants';
import { setH5NativeDeepLink } from './coreUtil';

export const getUpiIntentAppsMockAndroid = [
  {
    appName: 'CRED',
    processName: 'com.dreamplug.androidapp',
    status: true,
  },
  {
    appName: 'Axis Mobile',
    processName: 'com.axis.mobile',
    status: true,
  },
  {
    appName: 'iMobile',
    processName: 'com.csam.icici.bank.imobile',
    status: true,
  },
  {
    appName: 'GPay',
    processName: 'com.google.android.apps.nbu.paisa.user',
    status: true,
  },
  {
    appName: 'Digital Rupee By ICICI Bank',
    processName: 'com.icici.digitalrupee',
    status: true,
  },
  {
    appName: 'INDIE',
    processName: 'com.indusind.indie',
    status: true,
  },
  {
    appName: 'Vyom',
    processName: 'com.infrasoft.uboi',
    status: true,
  },
  {
    appName: 'Kotak Bank',
    processName: 'com.msf.kbank.mobile',
    status: true,
  },
  {
    appName: 'PhonePe',
    processName: 'com.phonepe.app',
    status: true,
  },
  {
    appName: 'HDFC Bank',
    processName: 'com.snapwork.hdfc',
    status: true,
  },
  {
    appName: 'Paytm',
    processName: 'net.one97.paytm',
    status: true,
  },
  {
    appName: 'Amazon Pay UPI',
    processName: 'in.amazon.mShop.android.shopping',
    status: true,
  },
];

function isPhoenixContainer() {
  const ua = window.navigator.userAgent;
  return /PhoenixContainer/i.test(ua);
}

const isH5Container = () => {
  const ua = window.navigator.userAgent;
  return /AppContainer/i.test(ua);
};

export function getBridge() {
  // if (typeof window !== 'undefined') {
  return isPhoenixContainer() ? window.JSBridge : window.AlipayJSBridge;
  // }
}

// export const isH5 = () => {
//   if (typeof window === 'undefined') {
//     return false;
//   }
//   log('Is H5 Check Called....');
//   log(`Is H5 Check Called....OS Checks....${getMobileOperatingSystem()}`);
//   try {
//     if (localStorage.getItem('isH5') === '1') {
//       return true;
//     }
//     if (
//       typeof getBridge() !== 'undefined'
//       // get(processQuarryPrams(window.location.search), 'os') ||
//       // get(window, 'App.os') !== 'mWEB'
//     ) {
//       localStorage.setItem('isH5', '1');
//       return true;
//     }
//     return false;
//   } catch (e) {
//     log(e);
//     return true;
//   }
// };

export const isH5 = () => {
  if (typeof window === 'undefined') {
    return false;
  }
  log('Is H5 Check Called....');
  log(`Is H5 Check Called....OS Checks....${getMobileOperatingSystem()}`);
  try {
    if (localStorage.getItem('isH5') === 1) {
      return true;
    }
    if (isH5Container() || isPhoenixContainer()) {
      // storatge is set from client.js
      localStorage.setItem('isH5', 1);
      return true;
    }
    return false;
  } catch (e) {
    log(e);
    return true;
  }
};

/**
 * @description - This function returns promise object which resolves bridge
 * result and if reject if bridge does not exist
 * @param {string} bridgeName
 * @returns {Promise} Promise object with bridge call result
 */
function bridgePromise(bridgeName) {
  return new Promise((resolve, reject) => {
    const bridge = getBridge();
    if (bridge) {
      bridge.call(bridgeName, (result) => {
        resolve(result);
      });
    } else {
      reject();
    }
  });
}

export const processOnBridgeReady = () => {
  log('##### processOnBridgeReady Removed.....');
  document.removeEventListener(
    isPhoenixContainer() ? 'JSBridgeReady' : 'AlipayJSBridgeReady',
    processOnBridgeReady,
  );
};

export const bridgeObserver = (callackFn) => {
  const bridgeName = getBridge();
  if (typeof bridgeName === 'undefined') {
    document.addEventListener(
      isPhoenixContainer() ? 'JSBridgeReady' : 'AlipayJSBridgeReady',
      callackFn,
    );
  } else if (callackFn) {
    callackFn();
  }
};

function paytmGetDeviceAuthenticationStatusCallback(result) {
  log('paytmGetAuthenticationStatus result---', JSON.stringify(result));
  if (result?.data?.biometric_status === 1) {
    sessionStorage.setItem('isBiometricExist', 'true');
  }
}

/**
 * @description - paytmGetAppInfo callback function
 * @param {Object} result bridge result
 */
const paytmGetAppInfoCallback = (result) => {
  if (result && result.data) {
    DeviceInfoProvider.setInfo('appVersionName', result.data.appVersionName);
    DeviceInfoProvider.setInfo('h5Version', result.data.h5Version);
    DeviceInfoProvider.setInfo(
      'deviceId',
      result.data.app_info_pt.deviceIdentifier,
    );
    return {
      appVersionName: result.data.appVersionName,
      h5Version: result.data.h5Version,
      deviceId: result.data.deviceIdentifier,
    };
  }
};
/**
 * @description - paytmFetchAuthToken callback function
 * @param {Object} result bridge result
 * @param {Function} reject reject function from promise
 */
const paytmFetchAuthTokenCallback = (result, reject) => {
  log(`getAuthTokenFromBridge called.....${JSON.stringify(result)}`);
  if (result && result.data) {
    log('Authorization.....', result.authorization, 'sso_token: ', result.data);

    // DeviceInfoProvider.setInfo('Authorization', result.authorization);
    // eslint-disable-next-line no-undef
    // if (__ENV__ !== 'staging') {
    DeviceInfoProvider.setInfo('sso_token', result.data);
    // }
    log('sso_token', result.data);

    let isLogin = true;

    const token = result.data;
    if (token === '' || token === 'null' || token === null) {
      isLogin = false;
      if (reject) {
        reject('No token found');
      }
    }
    localStorage.setItem('login', isLogin);
    const event = new Event('customLoginEvent');
    window.dispatchEvent(event);
    DeviceInfoProvider.setInfo('isLogin', isLogin);
    return {
      authorization: result.authorization,
      sso_token: result.data,
    };
  }
};

function initH5NativeDeepLinkUrl(deepLink) {
  log('##initH5NativeDeepLinkUrl');
  setH5NativeDeepLink(deepLink); // set in DeviceInfoProvider(since window._dispatch is not available on initial load)
}

export const getStartupParamsCallback = (callback) => {
  getBridge().call(
    'getStartupParams',
    {
      keys: ['deeplinkData'],
    },
    (result) => {
      log('initDeepLinkData result..MINI APP..', result);
      log('getStartupParamsCallback Data: ', result?.deeplinkData);
      if (result?.deeplinkData) {
        initH5NativeDeepLinkUrl(result.deeplinkData);
      }
      if (typeof callback === 'function') callback(result || {});
    },
  );
};

export const getStartupParamsAllCallback = (callback) =>
  new Promise((resolve, reject) => {
    const bridge = getBridge();
    if (bridge) {
      bridge.call(
        'getStartupParams',
        {
          keys: [],
        },
        (result) => {
          log('initDeepLinkData result..MINI APP..', result);
          log('getStartupParamsCallback Data: ', result?.deeplinkData);
          if (result?.deeplinkData) {
            initH5NativeDeepLinkUrl(result.deeplinkData);
          }
          if (typeof callback === 'function') callback(result || {});
        },
      );
    } else {
      reject();
    }
  });

/**
 * @description - paytmFetchCustomerId callback function
 * @param {Object} result bridge result
 */
const paytmFetchCustomerIdCallback = (result) => {
  if (result && result.data) {
    // eslint-disable-next-line no-undef
    // if (__ENV__ !== 'staging') {
    DeviceInfoProvider.setInfo('userId', result.data);
    // }
    log('userId', result.data);
    return {
      userId: result.data,
    };
  }
};

/**
 * @description - paytmDeviceName callback function
 * @param {Object} result bridge result
 */
const paytmDeviceNameCallback = (result) => {
  if (result && result.data) {
    DeviceInfoProvider.setInfo('deviceName', result.data);
    return {
      deviceName: result.data,
    };
  }
};

/**
 * @description - paytmOsVersion callback function
 * @param {Object} result bridge result
 */
const paytmOsVersionCallback = (result) => {
  if (result && result.data) {
    DeviceInfoProvider.setInfo('osVersion', result.data);
    return {
      osVersion: result.data,
    };
  }
};

/**
 * @description - paytmGetAppInfo callback function
 * @param {Object} result bridge result
 */
// const paytmGetLoginTypeCallback = (result) => {
//   if (result && result.data) {
//     DeviceInfoProvider.setInfo('loggedInType', result.data.loggedInType);
//   }
// };

/**
 * @description - fetch2FAToken callback function
 * @param {Object} result bridge result
 */
const fetch2FATokenCallback = (result) => {
  if (result && result.data) {
    DeviceInfoProvider.setInfo('twoFAToken', result?.data?.['x-2fa-token']);
    return {
      twoFAToken: result?.data?.['x-2fa-token'],
    };
  }
};

export function removeH5Loader() {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call(
      'paytmNavigateTo',
      {
        navigateTo: 'removeLoader',
        data: {},
      },
      () => {},
    );
  }
}

export const isBridge = () => isH5();

const INITIAL_BRIDGE_CALLS = {
  paytmDeviceName: paytmDeviceNameCallback,
  paytmOsVersion: paytmOsVersionCallback,
  paytmGetAppInfo: paytmGetAppInfoCallback,
  paytmGetDeviceAuthenticationStatus:
    paytmGetDeviceAuthenticationStatusCallback,
  // paytmGetLoginType: paytmGetLoginTypeCallback,
};

const INITIAL_AUTH_BRIDGE_CALLS = {
  paytmFetchAuthToken: paytmFetchAuthTokenCallback,
  paytmFetchCustomerId: paytmFetchCustomerIdCallback,
  pmFetch2faToken: fetch2FATokenCallback,
  getStartupParams: getStartupParamsCallback,
};

export const paytmLogin = (withRefresh = true) => {
  const queryParams = {
    ...getQueryParamsFromString(getInitialQueryParams()),
    ...getQueryParams(),
  };

  const redirectionUrl = `${window.location.origin}${
    window.location.pathname
  }${generateQueryParamsString(queryParams)}`;

  log('redirectionUrl', redirectionUrl);

  let url = LOGIN_URL.replace(
    '{redirectURL}',
    encodeURIComponent(redirectionUrl),
  );

  if (withRefresh) {
    // eslint-disable-next-line no-unused-vars
    url = `${url}&refresh=true`;
  }

  window.location.replace(url);
  return true;
};

export function setLoginAuthValues() {
  try {
    const ssoToken = getCookieValue(COOKIES.SSO_TOKEN);
    const userAgent = getCookieValue(COOKIES.USER_AGENT);

    let userId;
    if (userAgent) {
      userId = JSON.parse(userAgent).user_id;
    }

    if (ssoToken && userAgent && userId) {
      DeviceInfoProvider.setInfo('sso_token', ssoToken);

      const isLogin = true;
      localStorage.setItem('login', isLogin);
      const event = new Event('customLoginEvent');
      window.dispatchEvent(event);
      DeviceInfoProvider.setInfo('isLogin', isLogin);

      DeviceInfoProvider.setInfo('userId', userId);

      const twoFAToken = getCookieValue(COOKIES.TWO_FA_TOKEN);
      if (userAgent) {
        DeviceInfoProvider.setInfo('twoFAToken', twoFAToken);
      }
    } else {
      // eslint-disable-next-line no-use-before-define
      paytmLogin();
    }
  } catch (error) {
    // eslint-disable-next-line no-use-before-define
    paytmLogin();
  }
}

export const initDeviceParams = () =>
  new Promise((resolve, reject) => {
    log('init Params called.....', resolve);

    const initialBridgeCalls = Object.keys(INITIAL_BRIDGE_CALLS);

    const initialBridgeCallsPromises = initialBridgeCalls.map((bridgeName) =>
      bridgePromise(bridgeName),
    );

    Promise.all(initialBridgeCallsPromises)
      .then((results) => {
        results.forEach((result, index) =>
          INITIAL_BRIDGE_CALLS[initialBridgeCalls[index]](result, reject),
        );
        // eslint-disable-next-line no-undef
        // if (__ENV__ === 'staging') {
        //   // eslint-disable-next-line no-alert
        //   const ssoToken = prompt('Please enter custom sso token if you want');
        //   if (ssoToken) {
        //     DeviceInfoProvider.setInfo('sso_token', ssoToken);
        //   }
        //
        //   // eslint-disable-next-line no-alert
        //   const userID = prompt('Please enter custom userId if you want');
        //   if (userID) {
        //     DeviceInfoProvider.setInfo('userId', userID);
        //   }
        // }
        DeviceInfoProvider.setInfo('isBridgeCallSuccess', true);
        resolve();
      })
      .catch((err) => {
        log(err);
        DeviceInfoProvider.setInfo('isBridgeCallSuccess', false);
      });
  });

export const initialBridgeCall = () => {
  log('##### initialBridgeCall called.....');
  return new Promise((resolve, reject) => {
    log('init Params called.....', resolve);
    const initialAuthBridgeCalls = Object.keys(INITIAL_AUTH_BRIDGE_CALLS);

    const initialAuthBridgeCallsPromises = initialAuthBridgeCalls.map(
      (bridgeName) => bridgePromise(bridgeName),
    );

    if (!isPaytm()) {
      Promise.all(initialAuthBridgeCallsPromises)
        .then((results) => {
          results.forEach((result, index) =>
            INITIAL_AUTH_BRIDGE_CALLS[initialAuthBridgeCalls[index]](
              result,
              reject,
            ),
          );

          initDeviceParams()
            .then(() => {
              resolve();
            })
            .catch((err) => {
              log(err);
            });
        })
        .catch((err) => {
          log(err);
        });
    } else {
      setLoginAuthValues();
      initDeviceParams()
        .then(() => {
          resolve();
        })
        .catch((err) => {
          log(err);
        });
    }
  });
};

export function initBridges() {
  log('##### init bridge called.....');
  return new Promise((resolve) => {
    log('##### init bridge called.....resolved');
    bridgeObserver(() => {
      initialBridgeCall()
        .then(() => {
          removeH5Loader();
          resolve();
        })
        .catch((err) => {
          removeH5Loader();
          log('initDeepLinkData error', err);
        });
      processOnBridgeReady();
    });
  });
}

export const openDeepLink = (url, data) => {
  getBridge().call(
    'paytmNavigateTo',
    {
      navigateTo: 'openGoldDeeplink',
      clearBackStack: false, // to clear whole back stack of application
      finishThis: false, // to finish this activity
      data: data || { data: url },
    },
    () => {
      // Logger(`openDeepLink result....${JSON.stringify(result)}`);
    },
  );
};

export const openDeepLinkPaytm = (
  deepLinkUrl,
  popWindow = false,
  popToRoot = false,
) => {
  console.log('$$$ open deeplink paytm', deepLinkUrl);
  getBridge().call(
    'paytmOpenDeeplink',
    {
      deeplink: deepLinkUrl,
      popWindow,
      popToRoot,
    },
    () => {
      // Logger(result);
    },
  );
};

export const openDeepLinkPaytmMoney = (url, data, clearBackStack = false) => {
  console.log('openDeepLinkPaytmMoney called', url);
  getBridge().call(
    'paytmNavigateTo',
    {
      navigateTo: 'openPmDeeplink',
      clearBackStack, // to clear whole back stack of application
      finishThis: false, // to finish this activity
      data: data || { data: url },
    },
    (result) => {
      log(`openDeepLink result....${JSON.stringify(result)}`);
    },
  );
};

export function openInBrowser(url) {
  if (typeof getBridge() === 'undefined') {
    return;
  }
  getBridge().call('openInBrowser', {
    url,
  });
}

export const exitApp = () => {
  log('exitApp called', isIosBuild());
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call('popWindow');
  }
};

export function pushWindow(url, callback) {
  getBridge().call(
    'paytmPushWindow',
    {
      appId: 'ec7d3582692b487a90247ee78d850f5f',
      url,
      param: {
        pullRefresh: 'NO',
        canPullDown: 'NO',
        showTitleBar: true,
        readTitle: false,
        showLoading: 'NO',
        showProgress: 'YES',
      },
    },
    (result) => {
      log('bridge result:', result);
      if (callback && result?.data?.success) {
        callback(result);
      }
    },
  );
}

export const openNewPage = (url, callback) => {
  try {
    if (isBridge()) {
      pushWindow(url, callback);
    } else {
      window.open(url);
    }
  } catch (e) {
    log(e);
  }
};

// export const login = () =>
//   new Promise((resolve, reject) => {
//     log('login() called!!');
//     if (typeof getBridge() === 'undefined') {
//       // log('type of alipaybridge undefined!!');
//       reject(new Error('type of alipaybridge undefined!!'));
//     }

//     // DeviceInfoProvider.getInfo('device_type') === 'android' && isPaytmMoney()
//     //   ? (document.getElementById('loaderCssID').style.display = 'flex')
//     //   : '';
//     const bridge = getBridge();
//     bridge.call('paytmFetchAuthToken', (data) => {
//       if (data && data.data) {
//         log(
//           'Reauthorization.....',
//           data.authorization,
//           'sso_token: ',
//           data.data,
//         );

//         // DeviceInfoProvider.setInfo('Authorization', data.authorization);
//         // eslint-disable-next-line no-undef
//         // if (__ENV__ !== 'staging') {
//         DeviceInfoProvider.setInfo('sso_token', data.data);
//       } else {
//         window.JSBridge.call('paytmAuthHandler', (e) => {
//           localStorage.setItem('isH5', '1');
//           if (e?.data?.success) {
//             if (e.data?.authToken) {
//               DeviceInfoProvider.setInfo('sso_token', e.data.authToken);
//             }
//             DeviceInfoProvider.setInfo('isLogin', true);
//           }

//           window.location.reload();
//           // initRequiredParams(() => {
//           //   // document.getElementById('loaderCssID') && isPaytmMoney()
//           //   //   ? (document.getElementById('loaderCssID').style.display = 'none')
//           //   //   : null;
//           // });
//         });
//       }
//     });
//   });

export function login() {
  return new Promise((resolve, reject) => {
    log('login() called!!');
    if (typeof getBridge() === 'undefined') {
      // log('type of alipaybridge undefined!!');
      reject(new Error('type of alipaybridge undefined!!'));
    }

    // DeviceInfoProvider.getInfo('device_type') === 'android' && isPaytmMoney()
    //   ? (document.getElementById('loaderCssID').style.display = 'flex')
    //   : '';

    getBridge().call(
      'paytmNavigateTo',
      {
        navigateTo: 'paytmLogin',
        expectingResultBack: !0,
        data: {},
      },
      (e) => {
        localStorage.setItem('isH5', 1);
        if (e?.data?.data) {
          DeviceInfoProvider.setInfo('sso_token', e.data.data);
        } else if (e?.data) {
          DeviceInfoProvider.setInfo('sso_token', e.data);
        }
        if (e?.data) {
          DeviceInfoProvider.setInfo('isLogin', true);
        }
        window.location.reload();
        // initRequiredParams();
      },
    );
  });
}

function deleteCookie(name) {
  const expires = new Date().toUTCString();
  const path = '/';
  const cookie = `${name}=; expires=${expires}; path=${path}; domain=.paytmmoney.com`;
  const cookieOnCurrentDomain = `${name}=; expires=${expires}; path=${path};`;
  document.cookie = cookie;
  document.cookie = cookieOnCurrentDomain;
}

export const notifyNativeApp = (postData) => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call(
      'notifyNativeApp',
      {
        data: postData,
      },
      (result) => {
        console.log('notifyNativeApp result', result);
        return result;
      },
    );
  }
};

export const doLogin = (path) => {
  log('login fnc called!');
  if (isBridge() && !isPaytm()) {
    log('isBridge: true');
    return login()
      .then((res) => {
        log('login successed', res);
        // setIsConsentOverlayShown('false');
        return true;
      })
      .catch((err) => {
        log('login failed', err);
        return false;
      });
  }
  if (isBridge()) {
    // eslint-disable-next-line no-use-before-define
    deleteCookie(COOKIES.SSO_TOKEN);
    // eslint-disable-next-line no-use-before-define
    deleteCookie(COOKIES.USER_AGENT);

    paytmLogin(true);
    return true;
  }
  log('is bridge false: do login');
  const url = LOGIN_URL.replace('{redirectURL}', path);
  window.location.replace(url);
  return true;
};

export const logOutUser = () => {
  log('logout() called!!');
  doLogin();
};

export const openPaymentPML = (postData, callback) => {
  const bridge = getBridge();
  if (bridge) {
    bridge.call(
      'paytmNavigateTo',
      {
        navigateTo: 'edtechPaymentPage',
        clearBackStack: false, // to clear whole back stack of application
        finishThis: false, // to finish this activity
        data: postData,
      },
      (result) => {
        if (callback) {
          callback(result, postData?.CLIENT_ID, postData?.TXN_ID);
        }
      },
    );
  }
};

export const openPaytmPayment = (
  amount,
  orderId,
  txnToken,
  mid,
  clientUniqueTxnId,
  callback,
) => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call(
      'paytmPayment',
      {
        amount,
        orderId,
        txnToken,
        mid,
        type: 'standard',
      },
      (result) => {
        if (callback) {
          callback(result, clientUniqueTxnId, orderId);
        }
      },
    );
  }
};

export const getUpiIntentApps = (callback) => {
  callback({ data: JSON.stringify(getUpiIntentAppsMockAndroid) }); // for getting intentApps in local
  return;
  // log('getUpiIntentApps called');
  // const bridgeName = getBridge();
  // if (bridgeName) {
  //   getBridge().call('paytmFetchUpiApps', {}, (result) => {
  //     if (callback) {
  //       callback(result);
  //     }
  //   });
  // }
};

export const getUPIAppPresentIos = (postData, callback) => {
  const bridgeName = getBridge();
  if (bridgeName) {
    getBridge().call('checkUPIAppPresent', { data: postData }, (result) => {
      if (callback) {
        callback(result);
      }
    });
  }
};

export const openUpiIntentApp = (
  postData,
  callback,
  isAutopayIntent,
  isIosIntent,
) => {
  log('openUpiIntentApp called', postData, isAutopayIntent, isIosIntent);
  const bridgeName = getBridge();
  if (bridgeName) {
    getBridge().call(
      'launchUpiApp',
      {
        data: postData,
      },
      (result) => {
        if (callback) {
          callback(result, isIosIntent, isAutopayIntent);
        }
      },
    );
  }
};

export const openPaymentSDKFlow = (postData, callback, isIosPush) => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call(
      'paytmNavigateTo',
      {
        navigateTo: 'openPaytmMoneyPaymentFlow',
        data: postData,
      },
      (result) => {
        if (callback) {
          callback(result, isIosPush, null);
        }
      },
    );
  }
};

export const openPaytmMoneyPaymentAndGenericWebActivity = (
  postData,
  callback,
  isAutopayCollectFlow = false,
) => {
  log(
    'openPaytmMoneyPaymentAndGenericWebActivity called',
    postData,
    isAutopayCollectFlow,
  );
  const bridgeName = getBridge();
  if (bridgeName) {
    getBridge().call(
      'paytmNavigateTo',
      {
        navigateTo: 'pm_pg',
        data: postData,
      },
      (result) => {
        if (callback) {
          callback(result, null, null, isAutopayCollectFlow);
        }
      },
    );
  }
};

export function showPaytmToast(value, isShort = true) {
  if (!isIosBuild() && typeof getBridge() !== 'undefined') {
    getBridge().call('paytmToast', {
      message: value,
      isShort,
    });
  } else {
    // eslint-disable-next-line no-alert
    alert(value);
  }
}

export function refreshTwoFAtoken(callback = () => {}) {
  const bridgeName = getBridge();
  if (
    typeof bridgeName === 'undefined' ||
    DeviceInfoProvider.getInfo('isRefresh2FAPending')
  ) {
    return;
  }
  if (bridgeName) {
    DeviceInfoProvider.setInfo('isRefresh2FAPending', true);
    bridgeName.call(
      'paytmNavigateTo',
      {
        navigateTo: 'refresh_2FA_token',
        data: {},
      },
      (result) => {
        DeviceInfoProvider.setInfo('isRefresh2FAPending', false);
        if (callback) {
          callback(result);
        }
      },
    );
  }
}

export const pmlUpdateBiometricFlag = (callback) => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call(
      'paytmNavigateTo',
      {
        navigateTo: 'pmlUpdateBiometricFlag',
      },
      (result) => {
        if (callback) {
          console.log('pmlUpdateBiometricFlag', JSON.stringify(result));
          callback(result);
        }
      },
    );
  }
};

export const pmlUpdateTradingViewFlag = (callback) => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call('pmlUpdateTradingViewFlag', (result) => {
      if (callback) {
        console.log('pmlUpdateTradingViewFlag', JSON.stringify(result));
        callback(result);
      }
    });
  }
};

export const pmlIsTradingViewEnabled = (callback) => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call('pmlIsTradingViewEnabled', (result) => {
      if (callback) {
        console.log('pmlIsTradingViewEnabled', JSON.stringify(result));
        callback(result);
      }
    });
  }
};

export const pmlIsBiometricEnabled = (callback) => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call('pmlIsBiometricEnabled', (result) => {
      if (callback) {
        console.log('pmlIsBiometricEnabled', JSON.stringify(result));
        callback(result);
      }
    });
  }
};

export const pmlIsBiometricAvailable = (callback) => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call('pmlIsBiometricAvailable', (result) => {
      if (callback) {
        console.log('pmlIsBiometricAvailable', JSON.stringify(result));
        callback(result);
      }
    });
  }
};

// For IOS Only
export const paytmResetStatusBarBackgroundColor = () => {
  const bridgeName = getBridge();
  if (isBridge() && typeof bridgeName !== 'undefined') {
    bridgeName.call('paytmResetStatusBarBackgroundColor', {}, (result) => {
      log(JSON.stringify(result));
    });
  }
};

export const paytmChangeBottomBarColorBridge = (color) => {
  const bridgeName = getBridge();
  if (isBridge() && typeof bridgeName !== 'undefined') {
    log('paytmChangeBottomBarColorBridge isDarkMode:', isDarkMode());
    window.JSBridge.call(
      'changeBottomBarColor',
      { lightBottomBar: !isDarkMode(), bottomBarColor: color },
      (result) => {
        log(JSON.stringify(result));
      },
    );
  }
};

// TODO: REMOVE WHEN NATIVE ISSUE IS FIXED
export const paytmChangeStatusBarColorBridge = (color) => {
  const bridgeName = getBridge();
  if (isBridge() && typeof bridgeName !== 'undefined') {
    window.JSBridge.call(
      'paytmChangeStatusBarColor',
      {
        color,
        statusBarStyle: isDarkMode() ? 1 : 0,
      },
      () => {
        log('changes status bar color bridge called');
      },
    );
  }
};

export const getUserIrStatus = (callback) => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call('getUserIrStatus', (result) => {
      if (callback) {
        callback(result);
      }
    });
  } else {
    callback({});
  }
};

export const getUserPref = (keys, callback) => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call(
      'pmlGetData',
      {
        keys,
      },
      (result) => {
        if (callback) {
          callback(result);
          log('getUserPref', keys, 'result', result);
        }
      },
    );
  } else {
    callback({});
  }
};

export const createUserPref = (data, callback) => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call(
      'pmlSaveData',
      {
        data,
      },
      (result) => {
        if (callback) {
          callback(result);
          log('createUserPref', data, 'result', result);
        }
      },
    );
  }
};

export const pmlRemoveData = (keys, callback) => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call(
      'pmlRemoveData',
      {
        keys,
      },
      (result) => {
        if (callback) {
          callback(result);
        }
      },
    );
  }
};

export function qrLogin() {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call(
      'paytmNavigateTo',
      {
        navigateTo: 'qrLogin',
        data: {},
      },
      () => {},
    );
  }
}

export const getAppTheme = (callback) => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call('getAppTheme', (result) => {
      if (callback) {
        callback(result);
      }
    });
  }
};

export const updateAppTheme = (theme, callback) => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call('setAppTheme', { data: { theme } }, (result) => {
      if (callback) {
        callback(result);
      }
    });
  }
};

export const checkPermission = (type) =>
  new Promise((resolve) => {
    const bridgeName = getBridge();
    if (typeof bridgeName !== 'undefined') {
      bridgeName.call(
        'paytmCheckPermission',
        {
          permission: type,
        },
        (result) => {
          resolve(result);
        },
      );
    }
  });

export function getDeviceLocation() {
  const promise = new Promise((resolve) => {
    if (isBridge()) {
      getBridge().call(
        'paytmGetLocation',
        { mandatory_permission: true },
        (result) => {
          resolve(result);
        },
      );
    } else if (navigator.geolocation) {
      function showPosition(position) {
        resolve({
          data: {
            lat: position.coords.latitude,
            long: position.coords.longitude,
          },
        });
      }
      navigator.geolocation.getCurrentPosition(showPosition);
    } else {
      resolve(0);
    }
  });
  return promise;
}

export const reverseGeocodingBridge = (latitude, longitude) =>
  new Promise((resolve) => {
    log('reverseGeocodingBridge latitude', latitude);
    log('reverseGeocodingBridge longitude', longitude);
    const bridgeName = getBridge();
    if (typeof bridgeName !== 'undefined') {
      bridgeName.call(
        'paytmGetAddress',
        {
          latitude,
          longitude,
          mandatory_permission: true,
        },
        (result) => {
          log('reverseGeocodingBridge result', result);
          resolve(result);
        },
      );
    }
  });
