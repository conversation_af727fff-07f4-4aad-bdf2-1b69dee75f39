import { useEffect } from 'react';
import { BOTTOM_BAR_COLOR } from '../components/organisms/NewsWidget/enums';
import { isDarkMode } from '../utils/commonUtil';
import {
  paytmChangeBottomBarColorBridge,
  paytmChangeStatusBarColorBridge,
} from '../utils/bridgeUtils';

const useNewsWidgetBarColors = () => {
  useEffect(() => {
    const bottomBarColor = isDarkMode()
      ? BOTTOM_BAR_COLOR.NEWS_FULL_PAGE.OPEN.DARK
      : BOTTOM_BAR_COLOR.NEWS_FULL_PAGE.OPEN.LIGHT;
    const statusBarColor = isDarkMode()
      ? BOTTOM_BAR_COLOR.NEWS_FULL_PAGE.CLOSE.DARK
      : BOTTOM_BAR_COLOR.NEWS_FULL_PAGE.CLOSE.LIGHT;
    console.log('useNewsWidgetBarColors', { statusBarColor, bottomBarColor });
    paytmChangeBottomBarColorBridge(bottomBarColor);
    const timerId = setTimeout(() => {
      paytmChangeStatusBarColorBridge(statusBarColor);
    }, 100);

    return () => {
      clearTimeout(timerId);
    };
  }, []);
};

export default useNewsWidgetBarColors;
