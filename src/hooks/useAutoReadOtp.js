import { useEffect, useRef, useState } from 'react';

import { getBridge } from '@src/utils/bridgeUtils';
import { isIosBuild } from '@src/utils/commonUtil';
import { isPaytmMoney } from '@src/utils/coreUtil';
import { BASE_URL } from '@src/config/envConfig';

// uncomment next line for testing auto read otp in local environment
// import { useMockPaytmReadOTPMessage } from "./mockBridgeResponse/useMockPaytmReadOTPMessage";

const AUTO_READ_OTP_CONSTANTS = {
  TIME_LIMIT: 30, // in seconds
  SUBSCRIBE_STEP: 'start_read',
  UNSUBSCRIBE_STEP: 'stop_read',
  DURATION: 60, // in seconds
  SENDER_ID: 'PAYTMM,JM-MFCENT-S,PAYTMM-S',
  STATIC_DELAY: 3000,
  BRIDGE_POLLING: 3000,
  MAX_POLLING_COUNT: 7,
};

export const useAutoReadOtp = ({
  otpData = {},
  setOtpData = () => {},
  beforeOtpVerification = () => {},
}) => {
  // uncomment next line for testing auto read otp in local environment
  // const { mockGetBridge } = useMockPaytmReadOTPMessage();

  const [otpReadFromDevice, setReadOtpFromDevice] = useState('');
  const [otpAutoReadInProgress, setOtpAutoReadInProgress] = useState(false);
  const [autoReadTimer, setAutoReadTimer] = useState(
    AUTO_READ_OTP_CONSTANTS.TIME_LIMIT,
  );
  const [explicitelyStopAutoRead, setExplicitelyStopAutoRead] = useState(false);

  const intervalRef = useRef(null);

  const unsubscribePaytmReadOTP = ({
    setOtpAutoReadInProgressState = () => {},
  }) => {
    // uncomment next line for testing auto read otp in local environment
    // const bridgeName = mockGetBridge();

    const bridgeName = getBridge();
    const bridgePayload = {
      step: AUTO_READ_OTP_CONSTANTS.UNSUBSCRIBE_STEP,
    };

    if (bridgeName) {
      setOtpAutoReadInProgressState(false);
      bridgeName.call('paytmReadOTPMessage', bridgePayload, (result) => {
        console.log(
          'paytmReadOTPMessage unsubscribe  result---',
          JSON.stringify(result),
        );
      });
    }
  };

  const unsubscribePmlReadOTP = ({
    setOtpAutoReadInProgressState = () => {},
  }) => {
    console.log('paytmGetOtp unsubscribe---');
    setOtpAutoReadInProgressState(false);

    // const bridgeName = getBridge();
    // if (bridgeName) {
    // setOtpAutoReadInProgressState(false);
    //   bridgeName.unsubscribe("paytmGetOtp", (result) => {
    //     console.log("paytmGetOtp unsubscribe  result---", JSON.stringify(result));
    //   });
    // }
  };

  const triggerUnSubscribeAutoReadOtp = () => {
    console.log('trigger unsubscribe otp auto read bridge---');

    if (!otpAutoReadInProgress) {
      return;
    }

    if (!isIosBuild()) {
      if (isPaytmMoney()) {
        if (!BASE_URL.OTP_AUTO_READ.PML) return;

        console.log('unsubscribe otp auto read bridge for pml---');
        unsubscribePmlReadOTP({
          setOtpAutoReadInProgressState: setOtpAutoReadInProgress,
        });
      } else {
        if (!BASE_URL.OTP_AUTO_READ.PAYTM) return;

        console.log('unsubscribe otp auto read bridge for paytm---');
        unsubscribePaytmReadOTP({
          setOtpAutoReadInProgressState: setOtpAutoReadInProgress,
        });
      }
    }
  };

  const subscribePaytmReadOTP = ({
    setOtpFromDevice = () => {},
    setOtpAutoReadInProgressState = () => {},
  }) => {
    // uncomment next line for testing auto read otp in local environment
    // const bridgeName = mockGetBridge();

    const bridgeName = getBridge();
    const bridgePayload = {
      step: AUTO_READ_OTP_CONSTANTS.SUBSCRIBE_STEP,
      duration: AUTO_READ_OTP_CONSTANTS.DURATION,
      sender_id: AUTO_READ_OTP_CONSTANTS.SENDER_ID,
    };

    try {
      if (bridgeName) {
        setOtpAutoReadInProgressState(true);

        bridgeName.subscribe('paytmReadOTPMessage', bridgePayload, (result) => {
          console.log('paytmReadOTPMessage result---', result);

          if (result?.data) {
            const data = JSON.parse(result?.data);
            console.log('paytmReadOTPMessage data---', data);

            if (data?.success && data?.senderId && data?.message) {
              const autoReadOtp = data?.message.match(/\d{6}/);

              if (autoReadOtp) {
                setOtpFromDevice(autoReadOtp[0]);
                unsubscribePaytmReadOTP({ setOtpAutoReadInProgressState });
              }
            }
          } else if (result?.error) {
            setOtpAutoReadInProgressState(false);
          }
        });
      }
    } catch (err) {
      console.log('getOtpInPaytm err---', err);
      setOtpAutoReadInProgressState(false);
    }
  };

  const otpPollingState = useRef(null);

  const subscribePmlReadOTP = ({
    setOtpFromDevice = () => {},
    setOtpAutoReadInProgressState = () => {},
    digits = 6,
    bridgePollCount = 1,
  }) => {
    // uncomment next line for testing auto read otp in local environment
    // const bridgeName = mockGetBridge();

    const bridgeName = getBridge();

    if (bridgePollCount === 1) {
      console.log('paytmGetOtp polling started...');
      otpPollingState.current = true;
    }

    try {
      if (!bridgeName) {
        otpPollingState.current = false;
        return;
      }

      bridgeName.call('paytmGetOtp', (result) => {
        console.log('paytmGetOtp result---', JSON.stringify(result));

        const regexPattern = new RegExp(`\\d{${digits}}`);
        const autoReadOtp = JSON.stringify(result?.data)?.match(
          regexPattern,
        )?.[0];

        console.log(
          `paytmGetOtp previous OTP: ${otpReadFromDevice} | new OTP: ${autoReadOtp || null}`,
        );

        if (autoReadOtp) {
          console.log('paytmGetOtp setting autoReadOtp---', autoReadOtp);
          setOtpFromDevice(autoReadOtp);
          otpPollingState.current = false;
          unsubscribePmlReadOTP({ setOtpAutoReadInProgressState });
        } else if (result?.error) {
          otpPollingState.current = false;
          throw result?.error;
        } else {
          // Need to add this polling for the bridge response
          // Reason: Native team do not broadcast updated OTP, so we need to do polling

          if (bridgePollCount > AUTO_READ_OTP_CONSTANTS.MAX_POLLING_COUNT) {
            console.log('paytmGetOtp maximum polling attempts reached---');
            otpPollingState.current = false;
            return;
          }

          console.log(`paytmGetOtp poll count : ${bridgePollCount}`);

          if (otpPollingState.current) {
            setTimeout(() => {
              subscribePmlReadOTP({
                setOtpFromDevice: setReadOtpFromDevice,
                setOtpAutoReadInProgressState: setOtpAutoReadInProgress,
                bridgePollCount: bridgePollCount + 1,
              });
            }, AUTO_READ_OTP_CONSTANTS.BRIDGE_POLLING);
          }
        }
      });
    } catch (err) {
      console.log('getOtpInPaytm err---', err);
      otpPollingState.current = false;
      setOtpAutoReadInProgressState(false);
    }
  };

  const registerPmlOtpReceiver = () => {
    if (!BASE_URL.OTP_AUTO_READ.PML) return;

    // uncomment next line for testing auto read otp in local environment
    // const bridgeName = mockGetBridge();

    const bridgeName = getBridge();

    if (isPaytmMoney()) {
      if (otpAutoReadInProgress) {
        return;
      }

      try {
        setOtpAutoReadInProgress(true);

        if (bridgeName) {
          bridgeName.call('paytmRegisterOtpReceiver', (result) => {
            console.log(
              'paytmRegisterOtpReceiver  result---',
              JSON.stringify(result),
            );
          });
        }
      } catch (err) {
        console.log('paytmRegisterOtpReceiver err---', err);
      }
    }
  };

  const triggerSubscribeAutoReadOtp = () => {
    console.log('trigger subscribe otp auto read bridge---');
    setReadOtpFromDevice('');

    if (explicitelyStopAutoRead) {
      setExplicitelyStopAutoRead(false);
    }

    if (!isIosBuild()) {
      if (isPaytmMoney()) {
        if (!BASE_URL.OTP_AUTO_READ.PML) return;

        console.log('subscribe otp auto read bridge for pml---');
        setTimeout(() => {
          subscribePmlReadOTP({
            setOtpFromDevice: setReadOtpFromDevice,
            setOtpAutoReadInProgressState: setOtpAutoReadInProgress,
          });
        }, AUTO_READ_OTP_CONSTANTS.STATIC_DELAY);
      } else {
        if (!BASE_URL.OTP_AUTO_READ.PAYTM) return;

        if (otpAutoReadInProgress) {
          return;
        }

        console.log('subscribe otp auto read bridge for paytm---');
        subscribePaytmReadOTP({
          setOtpFromDevice: setReadOtpFromDevice,
          setOtpAutoReadInProgressState: setOtpAutoReadInProgress,
        });
      }
    }
  };

  // otpAutoReadInProgress interval
  useEffect(() => {
    if (otpAutoReadInProgress) {
      setAutoReadTimer(AUTO_READ_OTP_CONSTANTS.TIME_LIMIT); // Reset timer

      intervalRef.current = setInterval(() => {
        setAutoReadTimer((prev) => {
          if (prev <= 1) {
            clearInterval(intervalRef.current);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
      setAutoReadTimer(AUTO_READ_OTP_CONSTANTS.TIME_LIMIT);
    }

    return () => clearInterval(intervalRef.current);
  }, [otpAutoReadInProgress]);

  // setting auto read otp
  useEffect(() => {
    if (explicitelyStopAutoRead) {
      return;
    }

    if (otpReadFromDevice?.length > 0) {
      setOtpData({ ...otpData, otpReadFromDevice });
      setReadOtpFromDevice('');
      beforeOtpVerification();
    }
  }, [otpReadFromDevice, explicitelyStopAutoRead]);

  useEffect(() => {
    if (!autoReadTimer) {
      setExplicitelyStopAutoRead(true);
      triggerUnSubscribeAutoReadOtp();
    }
  }, [autoReadTimer]);

  return {
    otpAutoReadInProgress,
    autoReadTimer,
    registerPmlOtpReceiver,
    triggerUnSubscribeAutoReadOtp,
    triggerSubscribeAutoReadOtp,
  };
};
