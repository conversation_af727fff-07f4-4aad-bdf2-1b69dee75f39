import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient as ownQueryClient } from '../provider/ReactQueryProvider';
import { NewsWidgetPageContentComponent } from '../pages/NewsWidgetPage/MiniAppNewsWidgetPage';
import DeviceInfoProvider from '../provider/DeviceInfoProvider';
import AxiosProviderWrapper from '../contexts/AxiosProviderWrapper';
import NativeBackPressContext from '../contexts/NativeBackPressContext';
import { useNativeBackPress } from '../hooks/useNativeBackPress';
import AppContextProvider from '../contexts/AppContextProvider';
import NativeDocumentHideContext from '../contexts/NativeDocumentHideContext';
import { withErrorBoundary } from '../HOC/WidgetErrorBoundary';

const NewsFeedWidgetWrapper = (props) => {
  const BackPress = useNativeBackPress();
  console.log('window.location.href', window.location.href);
  const { bridgeData, dataFeed, ...rest } = props;
  if (bridgeData) {
    Object.entries(bridgeData).forEach(([key, val]) => {
      DeviceInfoProvider.setInfo(key, val);
    });
  }

  // Check only for the 'card' query parameter
  let isListPage = false;
  if (typeof window !== 'undefined') {
    const urlParams = new URLSearchParams(window.location.search);
    isListPage = urlParams.get('card') === 'news-list';
  }

  return (
    <QueryClientProvider client={ownQueryClient}>
      <AxiosProviderWrapper>
        <NativeDocumentHideContext>
          <AppContextProvider
            isFeedRequired={false} // Adjust if data feed is needed
            externalDataFeed={dataFeed}
          >
            <NativeBackPressContext.Provider value={BackPress}>
              <NewsWidgetPageContentComponent
                isNotifyHeight={false}
                isListPage={isListPage}
                {...rest}
              />
            </NativeBackPressContext.Provider>
          </AppContextProvider>
        </NativeDocumentHideContext>
      </AxiosProviderWrapper>
    </QueryClientProvider>
  );
};

export default withErrorBoundary(NewsFeedWidgetWrapper);
