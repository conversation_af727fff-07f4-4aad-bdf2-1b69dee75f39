.controlsRow {
  display: flex;
  align-items: center;
  padding: 15px 15px 0;
  min-height: 25px;
}

.controlsRowOverlap<PERSON>hart {
  position: relative;
  top: 10px;
  min-height: 0;
  height: 0;
  padding-top: 12px;
}

.expirySelector {
  display: flex;
  gap: 8px;
  margin-right: 16px;
}

.expiryButton {
  height: 25px;
  padding: 0 10px;
  border: 1px solid var(--border-neutral-variant);
  border-radius: 20px;
  color: var(--text-neutral-medium);
  background-color: transparent;
  font-size: 14px;
  transition: all 0.2s ease;

  &.activeExpiry {
    background: var(--background-primary-strong);
    color: var(--text-universal-strong);
    border: none;
  }
}

.legend {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.legendItem {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;

  &:not(:last-child) {
    margin-right: 16px;
  }
}

.legendDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.chartTypeSwitch {
  display: flex;
  gap: 8px;
  align-items: center;
  z-index: 1000;
}

.switchButton {
  color: var(--text-primary-strong);
  background-color: transparent;
  font-size: 14px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;

  span {
    font-size: 14px;
    font-weight: 500;
  }
}

.errorScreen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 180px;
}

.errorText {
  font-size: 14px;
  color: var(--text-neutral-medium);
  font-weight: 400;
  padding-top: 10px;
}

.refreshButton {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary-strong);
  padding-top: 10px;
}

.legendDiv {
  display: flex;
  justify-content: end;
}
