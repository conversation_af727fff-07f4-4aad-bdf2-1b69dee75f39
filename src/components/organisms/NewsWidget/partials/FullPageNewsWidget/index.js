import cx from 'classnames';
import { useState, useRef, useCallback, useEffect } from 'react';
import { isDarkMode } from '../../../../../utils/commonUtil';
import HeaderWrapper from '../../../../molecules/HeaderWrapper';
import Icon, { ICONS_NAME } from '../../../../molecules/Icon';
import NewsCard, { NewsCardLoading } from '../../../../molecules/NewsCard';
import { NEWS_CATEGORY, NEWS_CATEGORY_TABS } from '../../enums';
import { getNews } from '../../utils';
import styles from './index.scss';
import { useNewsWidgetEvents } from '../../useNewsWidgetEvents';

const FullPageNewsWidget = ({
  handleBackClick,
  handleCardClick,
  allNewsItems = [],
}) => {
  const [newsList, setNewsList] = useState(allNewsItems);
  const [activeTab, setActiveTab] = useState(NEWS_CATEGORY.HOT);
  const [isLoading, setIsloading] = useState(false);
  const [isFetchingMore, setIsFetchingMore] = useState(false);
  const [page, setPage] = useState(0);
  const [hasMoreData, setHasMoreData] = useState(true);
  const mainContainerRef = useRef(null);
  const loadMoreRef = useRef(null);
  const lastVhThresholdRef = useRef(0);

  const { onViewAllScroll, onViewAllClick, onTabChange } =
    useNewsWidgetEvents();
  useEffect(() => {
    setNewsList(allNewsItems);
  }, [allNewsItems]);

  const fetchNewsData = useCallback(
    async (section, from) => {
      try {
        const newsItems = await getNews(section, from);

        if (newsItems && newsItems.length > 0) {
          const updatedNewsList =
            from > 0 ? [...newsList, ...newsItems] : [...newsItems];

          setNewsList(updatedNewsList);
          setHasMoreData(newsItems.length > 0);
        } else {
          setHasMoreData(false);
        }
      } catch (error) {
        console.error('[fetchNewsData()] Error fetching news:', error);
        setHasMoreData(false);
      } finally {
        setIsloading(false);
        setIsFetchingMore(false);
      }
    },
    [newsList],
  );

  const handleIntersection = useCallback(
    (entries) => {
      const [entry] = entries;
      if (
        entry.isIntersecting &&
        !isFetchingMore &&
        !isLoading &&
        hasMoreData
      ) {
        setIsFetchingMore(true);
        const section = activeTab?.split(/\s/)[0]?.toUpperCase();
        const nextPage = page + 1;
        fetchNewsData(section, nextPage);
        setPage(nextPage);
      }
    },
    [isFetchingMore, isLoading, activeTab, fetchNewsData, page, hasMoreData],
  );

  const handleVhScroll = useCallback(() => {
    const mainContainer = mainContainerRef.current;
    if (!mainContainer) return;

    // Get the current scroll position
    const { scrollTop } = mainContainer;
    // Get 80% of the viewport height
    const vh80 = window.innerHeight * 0.8;
    // Calculate how many 80vh have been scrolled (integer division)
    const currentThreshold = Math.floor(scrollTop / vh80);

    if (currentThreshold > lastVhThresholdRef.current) {
      // User has crossed into a new 80vh threshold
      onViewAllScroll();
      lastVhThresholdRef.current = currentThreshold;
    }
  }, [onViewAllScroll]);

  useEffect(() => {
    const observer = new IntersectionObserver(handleIntersection, {
      root: mainContainerRef.current,
      rootMargin: '100px',
      threshold: 0.1,
    });

    const loadMoreEl = loadMoreRef.current;
    if (loadMoreEl) {
      observer.observe(loadMoreEl);
    }

    // Add scroll event listener with debounce
    const mainContainerElement = mainContainerRef.current;
    if (mainContainerElement) {
      mainContainerElement.addEventListener('scroll', handleVhScroll);
    }

    return () => {
      if (loadMoreEl) {
        observer.unobserve(loadMoreEl);
      }
      // Clean up scroll event listener
      if (mainContainerElement) {
        mainContainerElement.removeEventListener('scroll', handleVhScroll);
      }
    };
  }, [handleIntersection, handleVhScroll]);

  const handleTabClick = async (tab) => {
    onTabChange({ label: tab });

    setActiveTab(tab);
    setIsloading(true);
    setPage(0);
    setHasMoreData(true);
    const section = tab?.split(/\s/)[0]?.toUpperCase();
    try {
      const newsItems = await getNews(section, 0);
      setNewsList(newsItems);
      setHasMoreData(newsItems && newsItems.length > 0);
    } catch (error) {
      setNewsList([]);
      setHasMoreData(false);
      console.error('[handleTabClick()] Error fetching news:', error);
    } finally {
      setIsloading(false);
    }
  };

  return (
    <div
      className={cx(styles.fullPageContainer, {
        [styles.fullPageContainerDark]: isDarkMode(),
      })}
    >
      <div className={styles.fullPageHeader}>
        <HeaderWrapper
          showBack={false}
          customTitleComponent={
            <div className={styles.headerTitle}>
              <div className={styles.backButton} onClick={handleBackClick}>
                <Icon
                  name={
                    isDarkMode()
                      ? ICONS_NAME.BACK_ARROW_DARK
                      : ICONS_NAME.BACK_ARROW_LIGHT
                  }
                  width={18}
                  height={18}
                  iconStyles={styles.backButtonIcon}
                />
              </div>
              <div>Stock News</div>
            </div>
          }
          onBackClick={handleBackClick}
        />
      </div>

      <div className={styles.tabContainer}>
        {NEWS_CATEGORY_TABS.map(({ tab, label }) => (
          <div
            key={tab}
            className={cx(styles.tabChip, {
              [styles.tabChipDark]: isDarkMode(),
              [styles.activeTab]: activeTab === tab && !isDarkMode(),
              [styles.activeTabDark]: activeTab === tab && isDarkMode(),
            })}
            onClick={() => handleTabClick(tab)}
          >
            {label}
          </div>
        ))}
      </div>

      <div className={styles.mainContainer} ref={mainContainerRef}>
        {isLoading ? (
          [1, 2, 3, 4, 5].map((_, index) => (
            <NewsCardLoading key={index} isFullPage />
          ))
        ) : newsList?.length > 0 ? (
          <>
            {newsList?.map((newsItem, index) => (
              <NewsCard
                key={`${newsItem.id || index}-${newsItem.timestamp || index}`}
                item={newsItem}
                handleCardClick={handleCardClick}
                eventTrigger={onViewAllClick}
                isFullPage
              />
            ))}
            {hasMoreData && (
              <div ref={loadMoreRef} style={{ height: '20px' }} />
            )}
          </>
        ) : (
          [1, 2, 3, 4, 5].map((_, index) => (
            <NewsCardLoading key={index} isFullPage />
          ))
        )}
      </div>
    </div>
  );
};

export default FullPageNewsWidget;
