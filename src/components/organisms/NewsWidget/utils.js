import { NEWS_WIDGET_API } from '../../../config/urlConfig';
import { getGenericAppHeaders, makeApiGetCall } from '../../../utils/apiUtil';
import { openDeepLinkPaytmMoney } from '../../../utils/bridgeUtils';
import { generateQueryParamsString } from '../../../utils/commonUtil';
import { NEWS_WIDGET_ATTRIBUTES } from '../../../utils/constants';
import { isPaytmMoney } from '../../../utils/coreUtil';

export const formattedTimestamp = (date, time) => {
  const dateTime = new Date(`${date}T${time}`);

  // Get date part: "26 May"
  const datePart = dateTime.toLocaleDateString('en-US', {
    day: '2-digit',
    month: 'short',
  });

  // Get time part: "11:53 AM"
  const timePart = dateTime.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  });

  // Output: "26 May, 11:53 AM"
  return `${datePart}, ${timePart}`;
};

export function chunkArray(array, size) {
  const result = [];
  for (let i = 0; i < array.length; i += size) {
    result.push(array.slice(i, i + size));
  }
  return result;
}

export const getFormattedNewsItem = (scrip) => ({
  id: scrip.security_id || scrip.isin,
  companyName: scrip.stock_name,
  isHotNews: scrip.section_name.indexOf('Hot') > -1,
  news: scrip.heading,
  timestamp: formattedTimestamp(scrip.date, scrip.time),
  ...scrip,
});

export const formatNewsData = (data) => {
  const { data: { widget, meta } = {} } = data;
  const { attributes = [], sub_cohort_id = '' } = widget || {};

  const newsItems = attributes
    ?.find((item) => item.name === NEWS_WIDGET_ATTRIBUTES.TOP_NEWS)
    ?.value?.map((item) => getFormattedNewsItem(item));

  const widgetTitleArray = attributes?.find(
    (item) => item.name === NEWS_WIDGET_ATTRIBUTES.TITLE,
  )?.value;

  const viewAllCta = attributes?.find(
    (item) => item.name === NEWS_WIDGET_ATTRIBUTES.VIEW_ALL_TITLE,
  )?.value?.cta;

  const chunkedNewsItems = chunkArray(newsItems?.slice(0, 6) || [], 2);

  return {
    newsItems,
    chunkedNewsItems,
    widgetTitleArray,
    viewAllCta,
    subCohortId: sub_cohort_id,
    meta,
  };
};

export const fetchNews = async (
  param,
  from,
  allNewsItems = [],
  setAllNewsItems = () => {},
) => {
  const url = NEWS_WIDGET_API.CONCISE_EQUITY_NEWS;
  const headers = getGenericAppHeaders();
  const section = param || 'HOT';
  const queryParams = {
    section,
    from,
  };

  try {
    const response = await makeApiGetCall({
      url,
      headers,
      queryParams,
    });

    if (response?.data?.data) {
      const newsResp = response.data.data;
      const formattedItems = newsResp.results.map((item) =>
        getFormattedNewsItem(item),
      );

      const updatedNewsList =
        from > 0 ? [...allNewsItems, ...formattedItems] : [...formattedItems];

      setAllNewsItems(updatedNewsList);
    }
  } catch (error) {
    console.error('Error fetching news detail using makeApiGetCall:', error);
  }
};

export const getNews = async (section, from) => {
  const apiUrl = NEWS_WIDGET_API.CONCISE_EQUITY_NEWS;
  const queryParams = {
    section,
    from,
  };
  const headers = getGenericAppHeaders();
  try {
    const response = await makeApiGetCall({
      url: apiUrl,
      headers,
      queryParams,
    });
    if (response?.data?.data) {
      const newsResp = response?.data?.data;
      const formattedNewsItems = newsResp?.results.map((item) =>
        getFormattedNewsItem(item),
      );

      return formattedNewsItems;
    }
  } catch (error) {
    console.error('[getNews()] Error fetching news:', error);
    return [];
  }
};

export const getSingleNews = async (newsItem) => {
  const url = NEWS_WIDGET_API.DETAIL_EQUITY_NEWS;
  const queryParams = {
    isin: newsItem.isin,
    sno: newsItem.sno,
  };
  const headers = getGenericAppHeaders();
  try {
    const response = await makeApiGetCall({
      url,
      headers,
      queryParams,
    });

    if (response?.data?.data) {
      const newsResp = response?.data?.data;
      const updatedSingleNewsItem = {
        ...newsItem,
        fullNews: newsResp?.results[0]?.news_details,
      };
      return updatedSingleNewsItem;
    }
    console.error('Error fetching [updatedSingleNewsItem]', response);
  } catch (error) {
    console.error('Error fetching [updatedSingleNewsItem]', error);
  }
};

export const handleBuySellClick = (
  stock,
  transactionType,
  navigateTo,
  history,
) => {
  const {
    id,
    pml_id,
    security_id,
    exchange,
    companyName: name,
    segment,
    isin,
    instrument,
    product = 'C',
  } = stock || {};
  const queryString = generateQueryParamsString({
    id,
    securityId: security_id,
    exchange,
    name,
    segment,
    transactionType,
    isin,
    siblings: JSON.stringify(stock.siblings || []),
    tickSize: stock.tick_size || 0,
    instrumentType: instrument,
    symbol: null,
    activeLocation: 'company-page',
    quantity: 0,
    lotSize: null,
  });

  if (isPaytmMoney()) {
    const instrumentType =
      instrument === 'ES' ? 'company' : instrument.toLowerCase();
    const url = `https://paytmmoney.com/stocks/${instrumentType}/${pml_id}?action=place-order&txn_type=${transactionType}&price=0&product=${product}&order_type=MKT&exchange=${exchange}&shouldOpenOnCurrentScreen=true`;
    openDeepLinkPaytmMoney(url);
  } else {
    navigateTo(history, `/order-pad${queryString}`, {}, 'push');
  }
};

export function stringifySafe(obj) {
  const seen = new WeakSet();
  return JSON.stringify(obj, (key, value) => {
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) {
        // Duplicate reference found, skip it
        return;
      }
      seen.add(value);
    }
    return value;
  });
}
