import { useMemo, useRef, useEffect } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { isDarkMode } from '../../../utils/commonUtil';
import { THEMES } from '../../../utils/enums';
import styles from './index.scss';

const chartColors = {
  [THEMES.DARK]: {
    background: '#1a1a1a',
    text: '#EEEEEE8A',
    axis: '#EEEEEE4A',
    grid: '#22242A',
  },
  [THEMES.LIGHT]: {
    background: '#FFFFFF',
    text: '#1010108A',
    axis: '#1010104A',
    grid: '#F0F0F0',
  },
};

const HighchartsBarChart = ({
  data = null,
  height = 200,
  config = null,
  scrollToIndex = null,
  yAxisFormatter = (value) => value,
  scrollToXValue = null,
}) => {
  const theme = isDarkMode() ? THEMES.DARK : THEMES.LIGHT;
  const colors = chartColors[theme];
  const chartRef = useRef(null);
  const currentData = data;

  const chartConfig = useMemo(() => {
    const defaultConfig = {
      datasets: [
        {
          label: 'Calls OI',
          darkColor: '#B74040',
          lightColor: '#EB4B4B',
          key: 'callsOI',
        },
        {
          label: 'Puts OI',
          darkColor: '#02A85D',
          lightColor: '#2CB079',
          key: 'putsOI',
        },
      ],
    };

    return {
      ...defaultConfig,
      ...config,
    };
  }, [config]);

  const seriesData = useMemo(() => {
    if (
      !currentData ||
      !currentData.labels ||
      !Array.isArray(currentData.labels)
    )
      return [];

    return chartConfig.datasets.map((dataset) => {
      const rawData = currentData[dataset.key];
      const cleanData = Array.isArray(rawData)
        ? rawData.map((value) =>
            value !== null && value !== undefined && !Number.isNaN(value)
              ? Number(value)
              : null,
          )
        : [];

      return {
        name: dataset.label,
        data: cleanData,
        color: theme === THEMES.DARK ? dataset.darkColor : dataset.lightColor,
        borderRadius: 4,
      };
    });
  }, [currentData, theme, chartConfig]);

  const initialScrollPosition = useMemo(() => {
    if (
      scrollToXValue === null ||
      !currentData ||
      !Array.isArray(currentData.labels) ||
      currentData.labels.length === 0
    ) {
      return 1;
    }

    let minDiff = Infinity;
    let closestIdx = 0;
    currentData.labels.forEach((label, idx) => {
      const labelNum = typeof label === 'number' ? label : Number(label);
      if (Number.isNaN(labelNum)) {
        return;
      }
      const diff = Math.abs(labelNum - scrollToXValue);
      if (diff < minDiff) {
        minDiff = diff;
        closestIdx = idx;
      }
    });

    const dataLength = currentData.labels.length;
    const percentScroll = dataLength > 1 ? closestIdx / (dataLength - 1) : 0;
    return percentScroll;
  }, [scrollToXValue, currentData]);

  const chartOptions = useMemo(
    () => ({
      chart: {
        type: 'column',
        height,
        backgroundColor: colors.background,
        spacing: [10, 10, 15, 10],
        scrollablePlotArea: {
          minWidth: Math.max(800, (currentData?.labels?.length || 0) * 60),
          scrollPositionX: initialScrollPosition,
        },
        plotBorderWidth: 0,
      },
      title: {
        text: null,
      },
      credits: {
        enabled: false,
      },
      legend: {
        enabled: false,
      },
      xAxis: {
        categories: currentData?.labels || [],
        labels: {
          style: {
            color: colors.text,
            fontSize: '10px',
          },
        },
        lineColor: colors.axis,
        lineWidth: 1,
        tickColor: colors.axis,
        tickWidth: 1,
        tickLength: 5,
        gridLineWidth: 0,
        min: 0,
        max: Math.max(0, (currentData?.labels?.length || 1) - 1),
      },
      yAxis: {
        title: {
          text: null,
        },
        labels: {
          style: {
            color: colors.text,
            fontSize: '10px',
          },
          formatter: function formatYAxis() {
            // eslint-disable-next-line react/no-this-in-sfc
            return yAxisFormatter(this.value);
          },
        },
        opposite: true,
        gridLineWidth: 1,
        gridLineColor: colors.grid,
        lineColor: colors.axis,
        lineWidth: 1,
        tickColor: colors.axis,
        tickWidth: 1,
        tickLength: 5,
        tickInterval: null,
        minTickInterval: null,
        tickPixelInterval: 30,
        maxPadding: 0.05,
        minPadding: 0.05,
      },
      plotOptions: {
        column: {
          grouping: true,
          shadow: false,
          borderWidth: 0,
          pointPadding: 0.1,
          groupPadding: 0.1,
          borderRadius: 4,
          pointWidth: 10,
          states: {
            hover: {
              enabled: false,
            },
            inactive: {
              opacity: 1,
            },
          },
        },
        series: {
          animation: false,
          states: {
            inactive: {
              opacity: 1,
            },
          },
        },
      },
      tooltip: {
        enabled: false,
      },
      series: seriesData,
    }),
    [
      height,
      currentData?.labels,
      initialScrollPosition,
      colors,
      seriesData,
      yAxisFormatter,
    ],
  );

  useEffect(() => {
    if (chartRef.current && scrollToIndex === null && currentData?.labels) {
      const { chart } = chartRef.current;
      if (chart && currentData.labels.length > 0) {
        setTimeout(() => {
          const scrollableContainer = chart.container.querySelector(
            '.highcharts-scrollable-plot-area',
          );
          if (scrollableContainer) {
            scrollableContainer.scrollLeft =
              scrollableContainer.scrollWidth - scrollableContainer.clientWidth;
          }
        }, 300);
      }
    }
  }, [currentData, scrollToIndex]);

  if (
    !currentData ||
    !currentData.labels ||
    !Array.isArray(currentData.labels) ||
    currentData.labels.length === 0 ||
    seriesData.length === 0 ||
    seriesData.every((series) => !series.data || series.data.length === 0)
  ) {
    return (
      <div className={styles.emptyChart} style={{ height: `${height}px` }}>
        <span>No data available</span>
      </div>
    );
  }

  return (
    <div
      className={styles.highchartsContainer}
      style={{ height: `${height}px` }}
    >
      <HighchartsReact
        highcharts={Highcharts}
        options={chartOptions}
        ref={chartRef}
      />
    </div>
  );
};

export default HighchartsBarChart;
