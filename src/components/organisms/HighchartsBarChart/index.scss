.highchartsContainer {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  > div {
    width: 100% !important;
    height: 100% !important;
  }

  .highcharts-container {
    width: 100% !important;
    height: 100% !important;
  }

  .highcharts-scrollable-plot-area {
    overflow-x: auto !important;
    overflow-y: hidden !important;

    /* Hide scrollbar for Webkit browsers */
    &::-webkit-scrollbar {
      display: none !important;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.5);
    }

    /* Hide scrollbar for Firefox */
    scrollbar-width: none !important;

    /* Hide scrollbar for IE, Edge */
    -ms-overflow-style: none !important;
  }

  .dark-theme & {
    .highcharts-scrollable-plot-area {
      &::-webkit-scrollbar-thumb {
        background: rgba(238, 238, 238, 0.3);
      }

      &::-webkit-scrollbar-thumb:hover {
        background: rgba(238, 238, 238, 0.5);
      }
    }
  }
}

.emptyChart {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  background: transparent;

  span {
    color: #999;
    font-size: 14px;
    font-weight: 400;
  }

  .dark-theme & {
    span {
      color: #666;
    }
  }
}

.highcharts-container {
  .highcharts-background {
    fill: transparent !important;
  }

  .highcharts-plot-background {
    fill: transparent !important;
  }

  .highcharts-series-group {
    .highcharts-column-series {
      .highcharts-point {
        stroke-width: 0;
      }
    }
  }

  .highcharts-grid-line {
    stroke: transparent !important;
  }

  .highcharts-yaxis {
    position: relative !important;
    z-index: 100 !important;
  }

  .highcharts-yaxis-labels {
    text {
      background-color: transparent !important;
      padding-left: 8px !important;
    }
  }

  .highcharts-axis-line {
    stroke-width: 1px !important;
  }

  .highcharts-scrollbar {
    .highcharts-scrollbar-track {
      fill: transparent !important;
      stroke: transparent !important;
    }

    .highcharts-scrollbar-thumb {
      fill: rgba(255, 255, 255, 0.3) !important;
      stroke: transparent !important;
      rx: 4 !important;
    }

    .highcharts-scrollbar-button {
      fill: transparent !important;
      stroke: transparent !important;
    }

    .highcharts-scrollbar-rifles {
      stroke: transparent !important;
    }
  }

  .dark-theme & {
    .highcharts-scrollbar {
      .highcharts-scrollbar-thumb {
        fill: rgba(238, 238, 238, 0.3) !important;
      }
    }
  }

  @media (max-width: 768px) {
    .highcharts-axis-labels {
      text {
        font-size: 10px !important;
      }
    }
  }
}

.highcharts-container * {
  animation: none !important;
  transition: none !important;
}

* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}
*::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
