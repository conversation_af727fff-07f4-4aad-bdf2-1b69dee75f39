/* eslint-disable no-undef */
const env = __ENV__ === 'production' ? 'production' : 'production';

const URLs = {
  production: {
    EQ_HOST: 'https://api-eq.paytmmoney.com/',
    PM_API_HOST: 'https://api.paytmmoney.com/',
    PAYMENT_API_HOST: 'https://api.paytmmoney.com/',
    KYC_API_HOST: 'https://api-pf.paytmmoney.com/',
    LOGGER: 'https://logger.paytmmoney.com/',
    EQUITY_PML: 'https://api-eq.paytmmoney.com/',
    ORDERS_API_HOST: 'https://api-eq-order.paytmmoney.com/',
    ORDER_RESTRICTION_CONFIG:
      'https://static.paytmmoney.com/data/v1/production/orderrestrictions.json',
    PLATFORM: 'https://api-pf.paytmmoney.com',
    STATIC_HOST: 'https://static.paytmmoney.com/data/v1/production/',
    ORDER_PAD_DEEPLINKS: {
      ADD_NOMINEE_FLOW_ENABLED: true,
      ADD_NOMINEE_ERROR_CODE: "PM_TRXN_EC_400841",
      ADD_NOMINEE_DEEPLINK_URL: {
        PAYTM_MONEY: `paytmmoney:///mini-app?aId=ec7d3582692b487a90247ee78d850f5f&pageName=v3/mf-nominee-details`,
        PAYTM_MP: `paytmmp://paytmmoney/stocks/kyc/mf-nominee-details`,
      },
      NOMINEE_OPT_OUT_DEEPLINK: {
        PAYTM_MONEY: `paytmmoney:///mini-app?aId=ec7d3582692b487a90247ee78d850f5f&pageName=v3/mf-nominee-opt-out`,
        PAYTM_MP: `paytmmp://paytmmoney/stocks/kyc/mf-nominee-opt-out`,
      },
      READ_MORE_DEEPLINK: `https://www.sebi.gov.in/legal/circulars/jan-2025/circular-on-revise-and-revamp-nomination-facilities-in-the-indian-securities-market_90698.html`,
    },
    OTP_AUTO_READ: {
      PML: true,
      PAYTM: true,
    },
  },
  beta: {
    EQ_HOST: 'https://api-eq.paytmmoney.com/',
    PM_API_HOST: 'https://api.paytmmoney.com/',
    PAYMENT_API_HOST: 'https://api.paytmmoney.com/',
    KYC_API_HOST: 'https://api-pf.paytmmoney.com/',
    LOGGER: 'https://logger.paytmmoney.com/',
    EQUITY_PML: 'https://api-eq.paytmmoney.com/',
    ORDERS_API_HOST: 'https://api-eq-order.paytmmoney.com/',
    ORDER_RESTRICTION_CONFIG:
      'https://static.paytmmoney.com/data/v1/production/orderrestrictions.json',
    PLATFORM: 'https://api-pf.paytmmoney.com',
    STATIC_HOST: 'https://static.paytmmoney.com/data/v1/production/',
    ORDER_PAD_DEEPLINKS: {
      ADD_NOMINEE_FLOW_ENABLED: true,
      ADD_NOMINEE_ERROR_CODE: "PM_TRXN_EC_400841",
      ADD_NOMINEE_DEEPLINK_URL: {
        PAYTM_MONEY: `paytmmoney:///mini-app?aId=ec7d3582692b487a90247ee78d850f5f&pageName=v3/mf-nominee-details`,
        PAYTM_MP: `paytmmp://paytmmoney/stocks/kyc/mf-nominee-details`,
      },
      NOMINEE_OPT_OUT_DEEPLINK: {
        PAYTM_MONEY: `paytmmoney:///mini-app?aId=ec7d3582692b487a90247ee78d850f5f&pageName=v3/mf-nominee-opt-out`,
        PAYTM_MP: `paytmmp://paytmmoney/stocks/kyc/mf-nominee-opt-out`,
      },
      READ_MORE_DEEPLINK: `https://www.sebi.gov.in/legal/circulars/jan-2025/circular-on-revise-and-revamp-nomination-facilities-in-the-indian-securities-market_90698.html`,
    },
    OTP_AUTO_READ: {
      PML: true,
      PAYTM: true,
    },
  },
  staging: {
    EQ_HOST: 'https://api-eq-stg.paytmmoney.com/',
    PAYMENT_API_HOST: 'https://api-staging.paytmmoney.com/',
    PM_API_HOST: 'https://api-staging.paytmmoney.com/',
    KYC_API_HOST: 'https://pf-stg.paytmmoney.com/',
    LOGGER: 'https://api-eq-stg.paytmmoney.com/',
    EQUITY_PML: 'https://equity-stg.paytmmoney.com/',
    ORDERS_API_HOST: 'https://api-eq-order-stg.paytmmoney.com/',
    ORDER_RESTRICTION_CONFIG:
      'https://static.paytmmoney.com/data/v1/staging/orderrestrictions.json',
    PLATFORM: 'https://pf-stg.paytmmoney.com',
    STATIC_HOST: 'https://static.paytmmoney.com/data/v1/staging/',
    ORDER_PAD_DEEPLINKS: {
      ADD_NOMINEE_FLOW_ENABLED: true,
      ADD_NOMINEE_ERROR_CODE: "PM_TRXN_EC_400841",
      ADD_NOMINEE_DEEPLINK_URL: {
        PAYTM_MONEY: `paytmmoney:///mini-app?aId=ec7d3582692b487a90247ee78d850f5f&pageName=v3/mf-nominee-details`,
        PAYTM_MP: `paytmmp://paytmmoney/stocks/kyc/mf-nominee-details`,
      },
      NOMINEE_OPT_OUT_DEEPLINK: {
        PAYTM_MONEY: `paytmmoney:///mini-app?aId=ec7d3582692b487a90247ee78d850f5f&pageName=v3/mf-nominee-opt-out`,
        PAYTM_MP: `paytmmp://paytmmoney/stocks/kyc/mf-nominee-opt-out`,
      },
      READ_MORE_DEEPLINK: `https://www.sebi.gov.in/legal/circulars/jan-2025/circular-on-revise-and-revamp-nomination-facilities-in-the-indian-securities-market_90698.html`,
    },
  },
  OTP_AUTO_READ: {
    PML: true,
    PAYTM: true,
  },
};

const BROADCAST = {
  staging: {
    DATA_BROADCAST: 'wss://broadcast-miniapp.paytmmoney.com',
    ORDER_BROADCAST: 'wss://broadcast-order-stg.paytmmoney.com',
  },
  production: {
    DATA_BROADCAST: 'wss://broadcast-miniapp.paytmmoney.com',
    ORDER_BROADCAST: 'wss://broadcast-order.paytmmoney.com',
  },
  beta: {
    DATA_BROADCAST: 'wss://broadcast-miniapp.paytmmoney.com',
    ORDER_BROADCAST: 'wss://broadcast-order.paytmmoney.com',
  },
};

const BROADCAST_URL =
  BROADCAST[process.env.ENV === 'beta' ? process.env.ENV : env];

const BASE_URL = URLs[__ENV__ === 'beta' ? 'beta' : env];

const PAYMENTS_CONFIG_URL = {
  GET_CONFIG: `${URLs[env].STATIC_HOST}payments-config-v5.json`,
  GET_CONFIG_V6: `${URLs[env].STATIC_HOST}payments-config-v6.json`,
};

const getPaymentOptionHeaders = () => {
  if (__ENV__ === 'production' || __ENV__ === 'beta') {
    return {
      clientId: 'MF',
      subClientId: 'DAILY-SIP',
    };
  } else {
    return {
      clientId: 'STOCK_BROKING',
      subClientId: 'STOCK_BROKING-PAY',
    };
  }
};

export {
  BASE_URL,
  env,
  BROADCAST_URL,
  URLs,
  PAYMENTS_CONFIG_URL,
  getPaymentOptionHeaders,
};
