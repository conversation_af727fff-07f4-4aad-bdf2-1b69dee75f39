import { BASE_URL } from './envConfig';

const {
  KYC_API_HOST,
  LOGGER,
  PAYMENT_API_HOST,
  EQUITY_PML,
  EQ_HOST,
  ORDERS_API_HOST,
  PLATFORM,
  PM_API_HOST,
  STATIC_HOST,
} = BASE_URL;

export const GENERIC_API_URL = {
  APP_LOG: `${LOGGER}logger/log`,
  READINESS_V5: `${KYC_API_HOST}userprofile/user/user_id/v5/readiness?product=MUTUAL_FUND,EQUITY,NPS`,
  GET_ACCOUNT_MODIFICATION_STATUS: (userId) =>
    `${KYC_API_HOST}pf-kyc/v2/${userId}/account-modification/status`,
  EQUITY_INFO_CARD:
    'https://static.paytmmoney.com/data/v1/production/equity-infocard.json',
  GET_CUSTOMER_PLAN: (userId, productType) =>
    `${KYC_API_HOST}subscription/customer/${userId}/plan?productType=${productType}`,
  CANCEL_PAYMENT: (transactionId) =>
    `${PAYMENT_API_HOST}payments/api/cancel-transaction/${transactionId}`,
  GET_MF_SIP_LISTING: `https://api-staging.paytmmoney.com/mf/homepage/get-daily-schemes`,
  USER_BOOT: (userId) =>
    `${PM_API_HOST}pm/api/v2/users/boot/${userId}?details=personalDetails,userReadinessStatus`,
  // TODO: Why are we passing `EQUITY` in the MUTUAL_FUND app ?
  // BE needs it. in order to provide correct MUTUAL_FUND status.
  // As per BE - once all users are migrated to BSE then we don't need to pass EQUITY.
  USER_PROFILE: (userID) =>
    `${USER_PROFILE_API}/userprofile/user/${userID}/v3/readiness?product=MUTUAL_FUND,EQUITY`,
};

export const FAV_API_URLS = {
  GET_ALL_WATCHLIST: () => `${EQ_HOST}marketwatch/api/v1/watchlist?verbose=1`,
  CREATE_WATCHLIST: () => `${EQ_HOST}marketwatch/api/v1/watchlist`,
  RENAME_WATCHLIST: (watchlist_id) =>
    `${EQ_HOST}marketwatch/api/v1/watchlist/${watchlist_id}/rename`,
  DELETE_WATCHLIST: (watchlist_id) =>
    `${EQ_HOST}marketwatch/api/v1/watchlist/${watchlist_id}`,
  REMOVE_SECURITY_FRM_WATCHLIST: (watchlist_id, security_id) =>
    `${EQ_HOST}marketwatch/api/v1/watchlist/${watchlist_id}/security/${security_id}`,
  ADD_SECURITY_TO_WATCHLIST: (watchlist_id) =>
    `${EQ_HOST}marketwatch/api/v1/watchlist/${watchlist_id}/security`,
  GET_SECURITIES_IN_WATCHLIST: (watchlist_id) =>
    `${EQ_HOST}marketwatch/api/v1/watchlist/${watchlist_id}/security`,
  GET_SECURITIES_AT_WATCHLIST_INDEX: (index) =>
    `${EQ_HOST}marketwatch/api/v1/watchlist/index/${index}`,
  REORDER_WATCHLIST: `${EQ_HOST}marketwatch/api/v1/watchlist/reorder`,
  REORDER_WATCHLIST_STOCKS: (id) =>
    `${EQ_HOST}marketwatch/api/v1/watchlist/${id}/security/reorder`,
};

const getMyOrdersHost = () => {
  const BASE_API_URL_KYC_PROD = 'https://api-eq-order.paytmmoney.com/';
  const BASE_API_URL_KYC_STG = 'https://api-eq-order-stg.paytmmoney.com/';
  if (__ENV__ === 'production') {
    return BASE_API_URL_KYC_PROD;
  }
  if (__ENV__ === 'staging') {
    return BASE_API_URL_KYC_STG;
  }
  return BASE_API_URL_KYC_PROD;
};
const orderEqHost = getMyOrdersHost();

export const ORDERS_API_URLS = {
  ORDER_BOOK_DETAILS: `${orderEqHost}order/info/v1/orderbook`,
  CANCEL_ORDER: `${orderEqHost}order/txn/v1/cancel/regular`,
  GET_ORDER_BOOK: `${orderEqHost}order/info/v1/orderbook`,
};

export const getLoginHost = () => {
  const LOGIN_URL_PROD = 'https://h5-login.paytmmoney.com/';
  const LOGIN_URL_BETA = 'https://h5-login-beta.paytmmoney.com/';
  const LOGIN_URL_STG = 'https://h5-login-stag.paytmmoney.com/';
  if (__ENV__ === 'production') {
    return LOGIN_URL_PROD;
  }
  if (__ENV__ === 'beta') {
    return LOGIN_URL_BETA;
  }
  if (__ENV__ === 'staging') {
    return LOGIN_URL_STG;
  }
  return LOGIN_URL_PROD;
};

export const AGGREGATOR_API = {
  COMBINED_DASHBOARD: `${PAYMENT_API_HOST}aggr/home/<USER>/combined-dashboard`,
  COMBINED_DASHBOARD_FALLBACK:
    'https://static.paytmmoney.com/data/v1/production/native-apps/dashboard/combined-dashboard-v4-fallback.json',
  MF_DASHBOARD: `${PAYMENT_API_HOST}aggr/home/<USER>/dashboard`,
  MF_DASHBOARD_FUND: `${PAYMENT_API_HOST}aggr/mf/v3/dashboard`,
  ETF: `${PAYMENT_API_HOST}aggr/equity/etf/v1/dashboard`,
  ETF_FALLBACK:
    'https://static.paytmmoney.com/data/v1/production/native-apps/dashboard/etf-dashboard-v1-fallback.json',
  IPO: `${PAYMENT_API_HOST}aggr/equity/ipo/v1/dashboard`,
  IPO_FALLBACK:
    'https://static.paytmmoney.com/data/v1/production/native-apps/dashboard/ipo-dashboard-v1-fallback.json',
  STOCKS_DASHBOARD: `${PAYMENT_API_HOST}aggr/equity/stocks/v2/dashboard`,
  STOCKS_DASHBOARD_FALLBACK:
    'https://static.paytmmoney.com/data/v1/production/native-apps/dashboard/equity-dashboard-v2-fallback.json',
  FNO_DASHBOARD: `${PAYMENT_API_HOST}aggr/equity/fno/v1/dashboard`,
  FNO_DASHBOARD_FALLBACK:
    'https://static.paytmmoney.com/data/v1/production/native-apps/dashboard/fno-dashboard-v1-fallback.json',
  WIDGETS: `${PAYMENT_API_HOST}aggr/journey/v1/widgets?businessType=EQ_TRADE_CNF`,
};

export const COMPANY_LOGO_URL = {
  GET_LOGO: (name) => `https://static.paytmmoney.com/logos/company/${name}.png`,
};
export const LOGIN_URL = `${getLoginHost()}?returnUrl={redirectURL}`;

export const ORDER_VERIFICATION_SIKKIM = {
  GET_ADDRESS: (userId) => `${KYC_API_HOST}pf-kyc/v1/api/${userId}?q=ADDRESS`,
};

export const GET_CHARTS_IMAGE = `${EQUITY_PML}ssr-charts/v4/price?format=png&mode=light&range=1w`;
export const COMPANY_DETAILS_API = {
  GET_COMPANY_DETAILS: (company_id) =>
    `${EQ_HOST}data/v2/pml-details?ids=${company_id}`,
  GET_NUDGE_INFO: (security_id, segment, exchange, isin) => {
    if (isin) {
      return `${EQ_HOST}nudge-info/api/v2/scrip?scrip_id=${security_id}&segment=${segment}&exchange=${exchange}&isin=${isin}`;
    }
    return `${EQ_HOST}nudge-info/api/v2/scrip?scrip_id=${security_id}&segment=${segment}&exchange=${exchange}`;
  },
};

export const EQUITIES = {
  GET_MARKET_STATUS: `${EQ_HOST}exchange/mkt/v2/status`,
  FNO_OI: (symbol, expiry) =>
    `${EQ_HOST}fno/dashboard/api/v1/oi?symbol=${symbol}&expiry=${expiry}`,
  FNO_OPTIONS_CONFIG: (symbol) =>
    `${EQ_HOST}fno/dashboard/api/v1/option-chain/config?symbol=${symbol}`,
  FNO_PRF_INDICATORS: (symbol, expiry, interval, exchange) =>
    `${EQ_HOST}fno/dashboard/api/v1/option-chain/prf-indicators?interval=${interval}&symbol=${symbol}&expiry=${expiry}&exchange=${exchange}`,
  FII_DII: (count, investmentCategory, period, tradeCategory) =>
    `${EQ_HOST}fno/dashboard/api/v1/investment/inst-wise?count=${count}&inv_cat=${investmentCategory}&period=${period}&trade_cat=${tradeCategory}`,
  MARKET_MOVERS: (index, range) =>
    `${EQ_HOST}marketmovers/api/v1/index/gainersandlosers/range-list?index=${index}&range=${range}`,
  ORDERS_HOLD: {
    PLACE_REGULAR: `${EQ_HOST}orderhold/v1/order/place/regular`,
    CANCEL_REGULAR: `${EQ_HOST}orderhold/v1/order/cancel/regular`,
  },
  ORDERS: {
    PLACE: {
      POST_REGULAR: `${ORDERS_API_HOST}order/txn/v2/place/regular`,
      POST_BRACKET: `${ORDERS_API_HOST}order/txn/v1/place/bracket`,
      POST_COVER: `${ORDERS_API_HOST}order/txn/v1/place/cover`,
    },
    MODIFY: {
      REGULAR: `${ORDERS_API_HOST}order/txn/v2/modify/regular`,
      BRACKET: `${ORDERS_API_HOST}order/txn/v1/modify/bracket`,
      COVER: `${ORDERS_API_HOST}order/txn/v1/modify/cover`,
    },
    CONVERT: {
      POST: `${ORDERS_API_HOST}order/txn/v2/convert/regular`,
    },
    EXIT: {
      BRACKET: `${ORDERS_API_HOST}order/txn/v1/exit/bracket`,
      COVER: `${ORDERS_API_HOST}order/txn/v1/exit/cover`,
    },
  },
  CHARGES_INFO: {
    POST: `${EQ_HOST}fms/api/v1/charges/info`,
  },
  // POSITIONS: `${ORDERS_API_HOST}order/info/v1/position`,
  // POSITIONS_INTEROP: `${ORDERS_API_HOST}order/info/v1/interops/position`,
  // POSITIONS_DETAILS: `${ORDERS_API_HOST}order/info/v1/positiondetails`,
  // ORDER_HISTORY_INTEROP: `${ORDERS_API_HOST}order/info/v1/interops/positiondetails`,
  GET_PML_ID: `${EQ_HOST}data/v2/isin-pml-details`,
};

export const MY_FUNDS_DETAILS_API = {
  GET_FUND_SUMMARY: `${EQ_HOST}fms/api/v5/funds/summary`,
  GET_FUND_SUMMARY_DETAIL: `${EQ_HOST}fms/api/v1/funds/summary/detailed`,
  GET_TRANSACTION_SUMMARY: `${EQ_HOST}fms/api/v2/funds/txns`,
  GET_SINGLE_TRANSACTION_SUMMARY: `${EQ_HOST}fms/api/v2/funds/txn`,
  GET_QUARTERLY_MESSAGE_DETAIL: `${EQ_HOST}fms/api/v1/funds/qs/msg`,
  GET_ORDER_FUNDS_SUMMARY: `${EQ_HOST}fms/api/v1/orderpad/funds/summary`,
  GET_ORDER_FUNDS_SUMMARY_V2: `${EQ_HOST}fms/api/v2/orderpad/funds/summary`,
};

export const PRICES_URL = {
  GET_TARIFFS: `${PLATFORM}/platform/tariff/v1/brokerage-calculator-tariffs`,
  GET_MARGIN: `${EQ_HOST}margin/calculator/api/v1/order`,
};

export const PAYMENT_API_URLS = {
  INITIATE_FUND_FMS: `${EQ_HOST}fms/api/v3/funds/payin/initiate`,
  INITIATE_PAYMENT: `${EQ_HOST}fms/api/v1/funds/payin/initiate`,
  INITIATE_TRANSFER: `${EQ_HOST}fms/api/v1/funds/payout/initiate`,
  IS_FIRST_TRANSACTION: (txnId) =>
    `${EQ_HOST}fms/api/v1/user/payin/isfirsttxn?transaction_id=${txnId}`,
  PAYMENT_OPTIONS: `${PAYMENT_API_HOST}payments/api/v10/payment-option/`,
  PAYMENT_OPTIONS_MF: `${PAYMENT_API_HOST}payments/api/v8/payment-option/`,
  BALANCE_INFO: `${PAYMENT_API_HOST}payments/api/v3/balance-info`,
  MAKE_PAYMENT: `${PAYMENT_API_HOST}payments/api/v4/make-payment`,
  PAYMENT_NUDGE: (paymentsTxnId) =>
    `${PAYMENT_API_HOST}payments/api/v4/pg/status/query/${paymentsTxnId}`,
  VALIDATE_VPA: `${PAYMENT_API_HOST}payments/api/v4/validate-vpa`,
  GET_AMOUNT_LIMIT_SUGGESTION: `${EQ_HOST}fms/api/v4/funds/txn/suggestions`,
  FETCH_UPI_APPS_IOS:
    'https://static.paytmmoney.com/mini-app/data/fetchUPIOptionsIOS.json',
  GET_WITHDRAW_INFO: `${BASE_URL.STATIC_FILE_DATA_ETF}withdraw_infocard.json`,
  GET_PAYOUT_TIMELINE: `${BASE_URL.STATIC_FILE}mini-app/data/withdrawTimelinesConfig.json`,
  INTENT_FLOW_INFO: `${BASE_URL.STATIC_FILE}mini-app/data/intentFlowPayment.json`,
  PAYMENT_OPTIONS_FMS: `${EQ_HOST}fms/api/v1/payment-options`,
};

export const AUTO_PAY_API_URLS = {
  MANDATE_REG_OPTIONS: (amount) =>
    `${PAYMENT_API_HOST}payments/api/v12/payment-option?txnAmount=${amount}&isCheckoutFlowEnabled=false`,
};

export const MF_API_URLS = {
  FETCH_FUNDS: (pageNumber, type = 'DAILY') =>
    `${PM_API_HOST}mf/homepage/get-daily-schemes?type=${type}&pageNumber=${pageNumber}&pageSize=20`,
  GENERATE_OTP: (userId) =>
    `${PM_API_HOST}mftransaction/v3/${userId}/generateOTP`,
  BUY_MF_V7: (userID) =>
    `${PAYMENT_API_HOST}/mftransaction/v7/${userID}/purchase`,
};
export const PRICE_CHART_API = `${EQ_HOST}charts/price/v1/price-charts`;
export const CHARTS_CONFIG = `${STATIC_HOST}charts_session_maps.json`;

export const WIDGET_DATA = `${STATIC_HOST}widget-data.json`;

export const BANNERS_DATA = `${STATIC_HOST}transaction-banner.json`;

export const MTF_API_URL = {
  MTF_SCRIP: `${EQ_HOST}mtf/order/api/v1/scrip`,
};

export const NEWS_WIDGET_API = {
  CONCISE_EQUITY_NEWS: `${EQ_HOST}data/v1/conciseEquityNewsList?pageSize=50`,
  DETAIL_EQUITY_NEWS: `${EQ_HOST}data/v1/detailEquityNews`,
};
