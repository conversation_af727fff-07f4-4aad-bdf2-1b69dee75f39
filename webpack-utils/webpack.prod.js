const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CompressionPlugin = require('compression-webpack-plugin');
const { InjectManifest } = require('workbox-webpack-plugin');
const webpack = require('webpack');
// const fs = require("fs");
// const WebpackAssetsManifest = require("webpack-assets-manifest");

const commonPaths = require('./common-paths');
const APP_VERSION = require('./version');

const isDesktop = process.argv.includes('desktop');
const buildPath = isDesktop ? 'desktop' : 'mobile';
const NODE_ENV = process.env.NODE_ENV || 'development';

const getHostName = () => {
  switch (NODE_ENV) {
    case 'staging':
      return 'https://pml-widgets-stg.paytmmoney.com';
    case 'beta':
      return 'https://pml-widgets-beta.paytmmoney.com';
    case 'production':
      return 'https://pml-widgets.paytmmoney.com';
    default:
      return 'https://pml-widgets.paytmmoney.com';
  }
};

const getProdConfig = () => ({
  target: 'web',
  mode: 'production',

  plugins: [
    new CompressionPlugin({
      filename: '[path][base].gz[query]',
      algorithm: 'gzip',
      test: /\.(js|css)$/,
    }),
    new HtmlWebpackPlugin({
      hostName: getHostName(),
      template: `public/index.html`,
      filename: `index.html`,
      favicon: `public/favicon.ico`,
    }),
    new webpack.DefinePlugin({
      __MAINVERSION__: true,
    }),
    // new WebpackAssetsManifest({
    //   output: `${commonPaths.outputPath}/asset-manifest.json`,
    //   publicPath: true,
    //   writeToDisk: true,
    //   customize: (entry) => {
    //     // You can prevent adding items to the manifest by returning false.
    //     if (entry.key.toLowerCase().endsWith('.map')) return false;
    //     return entry;
    //   },
    //   done: (manifest, stats) => {
    //     // Write chunk-manifest.json.json
    //     const chunkFileName = `${commonPaths.outputPath}/chunk-manifest.json`;
    //     try {
    //       const fileFilter = (file) => !file.endsWith('.map');
    //       const addPath = (file) => manifest.getPublicPath(file);
    //       const chunkFiles = stats.compilation.chunkGroups.reduce((acc, c) => {
    //         acc[c.name] = [
    //           ...(acc[c.name] || []),
    //           ...c.chunks.reduce(
    //             (files, cc) => [
    //               ...files,
    //               ...cc.files.filter(fileFilter).map(addPath),
    //             ],
    //             [],
    //           ),
    //         ];
    //         return acc;
    //       }, Object.create(null));
    //       fs.writeFileSync(chunkFileName, JSON.stringify(chunkFiles, null, 2));
    //     } catch (err) {
    //       console.error(`ERROR: Cannot write ${chunkFileName}: `, err);
    //       process.exit(1);
    //     }
    //   },
    // }),
    new MiniCssExtractPlugin({
      filename: `${APP_VERSION}/${buildPath}/static/css/[name].[chunkhash:8].css`,
      chunkFilename: `${APP_VERSION}/${buildPath}/static/css/[id].[chunkhash:8].css`,
      ignoreOrder: true,
    }),

    new InjectManifest({
      swDest: `${commonPaths.outputPath}/sw-new.js`,
      swSrc: './public/serviceworker.js',
      exclude: [/asset-manifest\.json$/, /\.gz$/, /src\/assets\//],
      chunks: ['widget'],
    }),
  ],
});

module.exports = getProdConfig;
